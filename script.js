// DOM Content Loaded
document.addEventListener('DOMContentLoaded', function() {
    // Navigation functionality
    initNavigation();
    
    // Chat widget functionality
    initChatWidget();
    
    // Subscription buttons
    initSubscriptionButtons();
    
    // Smooth scrolling for anchor links
    initSmoothScrolling();
    
    // Intersection Observer for animations
    initScrollAnimations();

    // Initialize streaming bubbles
    initStreamingBubbles();

    // Initialize video background
    initVideoBackground();

    // Initialize FAQ functionality
    initFAQ();

    // Initialize advanced chat system
    initAdvancedChat();
});

// Navigation Functions
function initNavigation() {
    const navToggle = document.querySelector('.nav-toggle');
    const navMenu = document.querySelector('.nav-menu');
    const navLinks = document.querySelectorAll('.nav-link');

    // Mobile menu toggle
    if (navToggle) {
        navToggle.addEventListener('click', function() {
            navMenu.classList.toggle('active');
            
            // Animate hamburger menu
            const spans = navToggle.querySelectorAll('span');
            spans.forEach((span, index) => {
                span.style.transform = navMenu.classList.contains('active') 
                    ? `rotate(${index === 0 ? '45deg' : index === 1 ? '0deg' : '-45deg'})` 
                    : 'rotate(0deg)';
                if (index === 1) {
                    span.style.opacity = navMenu.classList.contains('active') ? '0' : '1';
                }
            });
        });
    }

    // Close mobile menu when clicking on links
    navLinks.forEach(link => {
        link.addEventListener('click', () => {
            navMenu.classList.remove('active');
            const spans = navToggle.querySelectorAll('span');
            spans.forEach((span, index) => {
                span.style.transform = 'rotate(0deg)';
                if (index === 1) span.style.opacity = '1';
            });
        });
    });

    // Navbar scroll effect
    window.addEventListener('scroll', function() {
        const navbar = document.querySelector('.navbar');
        if (window.scrollY > 100) {
            navbar.style.background = 'rgba(10, 10, 10, 0.98)';
            navbar.style.backdropFilter = 'blur(20px)';
        } else {
            navbar.style.background = 'rgba(10, 10, 10, 0.95)';
        }
    });
}

// Advanced Chat Widget Functions
function initChatWidget() {
    const chatToggle = document.getElementById('chat-toggle');
    const chatWindow = document.getElementById('chat-window');
    const chatClose = document.getElementById('chat-close');
    const chatInput = document.getElementById('chat-input');
    const chatSend = document.getElementById('chat-send');
    const chatBody = document.querySelector('.chat-body');

    // Toggle chat window
    chatToggle.addEventListener('click', function() {
        chatWindow.classList.toggle('active');
        if (chatWindow.classList.contains('active')) {
            chatInput.focus();
            // Show welcome message if first time
            if (chatBody.children.length <= 1) {
                setTimeout(() => {
                    addMessage("Hi! I'm your BadboyzTv assistant. I can help answer questions about our service, pricing, and setup. What would you like to know?", 'bot');
                }, 500);
            }
        }
    });

    // Close chat window
    chatClose.addEventListener('click', function() {
        chatWindow.classList.remove('active');
    });

    // Send message functionality
    function sendMessage() {
        const message = chatInput.value.trim();
        if (message) {
            // Add user message
            addMessage(message, 'user');
            chatInput.value = '';

            // Show typing indicator
            showTypingIndicator();

            // Process message with AI-like response
            setTimeout(() => {
                hideTypingIndicator();
                const response = processUserMessage(message);
                addMessage(response.text, 'bot');

                // Add quick action buttons if needed
                if (response.actions) {
                    addQuickActions(response.actions);
                }
            }, 1500);
        }
    }

    // Add message to chat
    function addMessage(text, sender) {
        const messageDiv = document.createElement('div');
        messageDiv.className = `chat-message ${sender}`;
        messageDiv.innerHTML = `<p>${text}</p>`;
        chatBody.appendChild(messageDiv);
        chatBody.scrollTop = chatBody.scrollHeight;
    }

    // Show typing indicator
    function showTypingIndicator() {
        const typingDiv = document.createElement('div');
        typingDiv.className = 'chat-message bot typing-indicator';
        typingDiv.id = 'typing-indicator';
        typingDiv.innerHTML = '<div class="typing-dots"><span></span><span></span><span></span></div>';
        chatBody.appendChild(typingDiv);
        chatBody.scrollTop = chatBody.scrollHeight;
    }

    // Hide typing indicator
    function hideTypingIndicator() {
        const typingIndicator = document.getElementById('typing-indicator');
        if (typingIndicator) {
            typingIndicator.remove();
        }
    }

    // Add quick action buttons
    function addQuickActions(actions) {
        const actionsDiv = document.createElement('div');
        actionsDiv.className = 'chat-actions';

        actions.forEach(action => {
            const button = document.createElement('button');
            button.className = 'chat-action-btn';
            button.textContent = action.text;
            button.onclick = () => {
                if (action.type === 'telegram') {
                    window.open('https://t.me/badboyztbv', '_blank');
                } else if (action.type === 'whatsapp') {
                    window.open('https://chat.whatsapp.com/F5sInZszoNkHWCCOkjblSO', '_blank');
                } else if (action.type === 'phone') {
                    window.open('tel:+12724232975', '_self');
                } else if (action.type === 'facebook') {
                    window.open('https://www.facebook.com/share/16iPBo8oz6/', '_blank');
                } else if (action.type === 'faq') {
                    document.getElementById('faq').scrollIntoView({ behavior: 'smooth' });
                    chatWindow.classList.remove('active');
                }
            };
            actionsDiv.appendChild(button);
        });

        chatBody.appendChild(actionsDiv);
        chatBody.scrollTop = chatBody.scrollHeight;
    }

    // Send message on button click
    chatSend.addEventListener('click', sendMessage);

    // Send message on Enter key
    chatInput.addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            sendMessage();
        }
    });
}

// Subscription Button Functions
function initSubscriptionButtons() {
    const subscribeButtons = document.querySelectorAll('.subscribe-btn');
    const resellerButtons = document.querySelectorAll('.reseller-btn');
    
    // Handle subscription buttons (redirect to Telegram)
    subscribeButtons.forEach(button => {
        button.addEventListener('click', function() {
            const plan = this.getAttribute('data-plan');
            
            // Add loading state
            const originalText = this.innerHTML;
            this.innerHTML = '<span class="loading"></span> Redirecting...';
            this.disabled = true;
            
            // Simulate processing time then redirect
            setTimeout(() => {
                window.open('https://t.me/badboyztbv', '_blank');
                
                // Reset button
                this.innerHTML = originalText;
                this.disabled = false;
            }, 1500);
        });
    });

    // Handle reseller buttons (redirect to Telegram)
    resellerButtons.forEach(button => {
        button.addEventListener('click', function() {
            const package = this.getAttribute('data-package');
            
            // Add loading state
            const originalText = this.innerHTML;
            this.innerHTML = '<span class="loading"></span> Redirecting...';
            this.disabled = true;
            
            // Simulate processing time then redirect
            setTimeout(() => {
                window.open('https://t.me/badboyztbv', '_blank');
                
                // Reset button
                this.innerHTML = originalText;
                this.disabled = false;
            }, 1500);
        });
    });
}

// Smooth Scrolling Functions
function initSmoothScrolling() {
    const links = document.querySelectorAll('a[href^="#"]');
    
    links.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            
            const targetId = this.getAttribute('href');
            const targetSection = document.querySelector(targetId);
            
            if (targetSection) {
                const offsetTop = targetSection.offsetTop - 80; // Account for fixed navbar
                
                window.scrollTo({
                    top: offsetTop,
                    behavior: 'smooth'
                });
            }
        });
    });
}

// Scroll Animations
function initScrollAnimations() {
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };

    const observer = new IntersectionObserver(function(entries) {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('fade-in-up');
                observer.unobserve(entry.target);
            }
        });
    }, observerOptions);

    // Observe elements for animation
    const animateElements = document.querySelectorAll('.feature-card, .pricing-card, .benefit-card, .step');
    animateElements.forEach(el => {
        observer.observe(el);
    });
}

// Utility Functions
function showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.className = `notification ${type}`;
    notification.innerHTML = `
        <div class="notification-content">
            <span>${message}</span>
            <button class="notification-close">&times;</button>
        </div>
    `;
    
    document.body.appendChild(notification);
    
    // Auto remove after 5 seconds
    setTimeout(() => {
        notification.remove();
    }, 5000);
    
    // Manual close
    notification.querySelector('.notification-close').addEventListener('click', () => {
        notification.remove();
    });
}

// Floating cards animation enhancement
function enhanceFloatingCards() {
    const cards = document.querySelectorAll('.floating-card');
    
    cards.forEach((card, index) => {
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-10px) scale(1.05)';
            this.style.boxShadow = '0 20px 40px rgba(212, 175, 55, 0.3)';
        });
        
        card.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0) scale(1)';
            this.style.boxShadow = 'none';
        });
    });
}

// Initialize floating cards enhancement
document.addEventListener('DOMContentLoaded', enhanceFloatingCards);

// Pricing card hover effects
function enhancePricingCards() {
    const pricingCards = document.querySelectorAll('.pricing-card');
    
    pricingCards.forEach(card => {
        card.addEventListener('mouseenter', function() {
            if (!this.classList.contains('featured')) {
                this.style.transform = 'translateY(-10px)';
                this.style.boxShadow = '0 20px 40px rgba(212, 175, 55, 0.2)';
            }
        });
        
        card.addEventListener('mouseleave', function() {
            if (!this.classList.contains('featured')) {
                this.style.transform = 'translateY(0)';
                this.style.boxShadow = 'none';
            }
        });
    });
}

// Initialize pricing cards enhancement
document.addEventListener('DOMContentLoaded', enhancePricingCards);

// Performance optimization - Lazy loading for images
function initLazyLoading() {
    const images = document.querySelectorAll('img[data-src]');
    
    const imageObserver = new IntersectionObserver((entries, observer) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                const img = entry.target;
                img.src = img.dataset.src;
                img.classList.remove('lazy');
                imageObserver.unobserve(img);
            }
        });
    });
    
    images.forEach(img => imageObserver.observe(img));
}

// Initialize lazy loading
document.addEventListener('DOMContentLoaded', initLazyLoading);

// Streaming Bubbles Functions
function initStreamingBubbles() {
    const bubbles = document.querySelectorAll('.bubble');

    bubbles.forEach((bubble, index) => {
        // Add random animation variations
        const randomDelay = Math.random() * 2;
        const randomDuration = 6 + Math.random() * 3;

        bubble.style.animationDelay = randomDelay + 's';
        bubble.style.animationDuration = randomDuration + 's';

        // Add click interaction
        bubble.addEventListener('click', function(e) {
            e.stopPropagation();

            // Create ripple effect
            const ripple = document.createElement('div');
            ripple.className = 'bubble-ripple';
            this.appendChild(ripple);

            // Animate ripple
            setTimeout(() => {
                ripple.remove();
            }, 600);

            // Show platform info
            showPlatformInfo(this);
        });

        // Add hover effects
        bubble.addEventListener('mouseenter', function() {
            this.style.transform = 'scale(1.3)';
            this.style.borderColor = 'var(--primary-gold)';
            this.style.boxShadow = '0 0 25px rgba(212, 175, 55, 0.6)';
        });

        bubble.addEventListener('mouseleave', function() {
            this.style.transform = 'scale(1)';
            this.style.borderColor = 'rgba(212, 175, 55, 0.3)';
            this.style.boxShadow = 'none';
        });
    });

    // Add floating particles
    createFloatingParticles();
}

// Show platform information
function showPlatformInfo(bubble) {
    const platformNames = {
        'netflix': 'Netflix Content Available',
        'hulu': 'Hulu Shows & Movies',
        'disney': 'Disney+ Collection',
        'amazon': 'Prime Video Library',
        'hbo': 'HBO Max Originals',
        'apple': 'Apple TV+ Exclusives',
        'paramount': 'Paramount+ Content',
        'peacock': 'Peacock Streaming'
    };

    const className = Array.from(bubble.classList).find(cls => platformNames[cls]);
    const platformName = platformNames[className] || 'Premium Content';

    // Create floating text
    const floatingText = document.createElement('div');
    floatingText.className = 'floating-platform-text';
    floatingText.textContent = platformName;
    floatingText.style.position = 'absolute';
    floatingText.style.top = bubble.offsetTop - 30 + 'px';
    floatingText.style.left = bubble.offsetLeft + 'px';
    floatingText.style.color = 'var(--primary-gold)';
    floatingText.style.fontWeight = 'bold';
    floatingText.style.fontSize = '14px';
    floatingText.style.zIndex = '10';
    floatingText.style.pointerEvents = 'none';
    floatingText.style.animation = 'fadeInUp 0.5s ease-out';

    bubble.parentElement.appendChild(floatingText);

    setTimeout(() => {
        floatingText.remove();
    }, 2000);
}

// Create floating particles
function createFloatingParticles() {
    const heroSection = document.querySelector('.hero');
    if (!heroSection) return;

    for (let i = 0; i < 20; i++) {
        const particle = document.createElement('div');
        particle.className = 'floating-particle';
        particle.style.position = 'absolute';
        particle.style.width = Math.random() * 4 + 2 + 'px';
        particle.style.height = particle.style.width;
        particle.style.background = 'rgba(212, 175, 55, 0.3)';
        particle.style.borderRadius = '50%';
        particle.style.left = Math.random() * 100 + '%';
        particle.style.top = Math.random() * 100 + '%';
        particle.style.animation = `particle-float ${8 + Math.random() * 4}s infinite ease-in-out`;
        particle.style.animationDelay = Math.random() * 5 + 's';
        particle.style.zIndex = '1';

        heroSection.appendChild(particle);
    }
}

// Video Background Functions
function initVideoBackground() {
    const videos = document.querySelectorAll('.hero-video video');

    videos.forEach(video => {
        // Ensure video plays on mobile
        video.addEventListener('loadeddata', function() {
            this.play().catch(e => {
                console.log('Video autoplay prevented:', e);
                // Fallback: show static background
                this.style.display = 'none';
                this.parentElement.style.background = 'var(--gradient-dark)';
            });
        });

        // Handle video errors
        video.addEventListener('error', function() {
            console.log('Video failed to load, using fallback background');
            this.style.display = 'none';
            this.parentElement.style.background = 'var(--gradient-dark)';
        });

        // Optimize video performance
        video.addEventListener('canplay', function() {
            this.style.opacity = '0.3';
        });
    });
}

// Enhanced bubble animations
function enhanceBubbleAnimations() {
    const bubbles = document.querySelectorAll('.bubble');

    setInterval(() => {
        bubbles.forEach(bubble => {
            // Random pulse effect
            if (Math.random() < 0.1) {
                bubble.style.animation = 'bubble-pulse 1s ease-in-out';
                setTimeout(() => {
                    bubble.style.animation = '';
                }, 1000);
            }
        });
    }, 3000);
}

// Initialize enhanced animations
document.addEventListener('DOMContentLoaded', enhanceBubbleAnimations);

// FAQ Functions
function initFAQ() {
    const faqItems = document.querySelectorAll('.faq-item');

    faqItems.forEach(item => {
        const question = item.querySelector('.faq-question');

        question.addEventListener('click', function() {
            // Close other open items
            faqItems.forEach(otherItem => {
                if (otherItem !== item) {
                    otherItem.classList.remove('active');
                }
            });

            // Toggle current item
            item.classList.toggle('active');
        });
    });
}

// Advanced Chat System
function initAdvancedChat() {
    // Initialize FAQ database for chat responses
    window.faqDatabase = {
        'what is badboyz': {
            answer: "BadboyzTv is a premium streaming service that provides access to thousands of channels and on-demand content from around the world. We offer high-quality streaming with multiple device support and 24/7 customer service.",
            confidence: 0.9
        },
        'how many devices': {
            answer: "It depends on your plan: Essential plan allows 2 connections, Premium plan allows 4 connections, and Elite plan allows 5 simultaneous connections across all your devices.",
            confidence: 0.9
        },
        'what devices supported': {
            answer: "BadboyzTv works on all major devices including Smart TVs, Android/iOS phones and tablets, computers (Windows/Mac), Amazon Fire Stick, Android TV boxes, and more.",
            confidence: 0.9
        },
        '4k content': {
            answer: "Yes! Our Premium and Elite plans include 4K Ultra HD content. The Elite plan also includes HDR support for the best viewing experience.",
            confidence: 0.9
        },
        'how to subscribe': {
            answer: "Simply click on any 'Select Plan' button above, and you'll be redirected to our Telegram group where our team will help you complete your subscription quickly and securely.",
            confidence: 0.9,
            actions: [
                { text: 'Contact on Telegram', type: 'telegram' },
                { text: 'View Plans', type: 'faq' }
            ]
        },
        'payment methods': {
            answer: "We accept various payment methods including PayPal, cryptocurrency, bank transfers, and other secure payment options. Contact us via Telegram for specific payment details.",
            confidence: 0.9,
            actions: [
                { text: 'Contact on Telegram', type: 'telegram' }
            ]
        },
        'free trial': {
            answer: "Yes! We offer a 24-hour free trial so you can test our service quality before committing to a subscription. Contact us on Telegram to activate your trial.",
            confidence: 0.9,
            actions: [
                { text: 'Get Free Trial', type: 'telegram' }
            ]
        },
        'technical issues': {
            answer: "Our 24/7 support team is always ready to help! You can reach us through this live chat, WhatsApp, or directly via our Telegram group for immediate assistance.",
            confidence: 0.9,
            actions: [
                { text: 'WhatsApp Support', type: 'whatsapp' },
                { text: 'Telegram Support', type: 'telegram' }
            ]
        },
        'cancel subscription': {
            answer: "Yes, you can cancel your subscription at any time. There are no long-term contracts or cancellation fees. Simply contact our support team via Telegram.",
            confidence: 0.9,
            actions: [
                { text: 'Contact Support', type: 'telegram' }
            ]
        },
        'sports channels': {
            answer: "Absolutely! We offer extensive sports coverage including NFL, NBA, MLB, soccer, boxing, UFC, and many international sports channels with live events and replays.",
            confidence: 0.9
        },
        'reliable service': {
            answer: "Yes! We maintain 99.9% uptime with multiple server locations worldwide. Our advanced infrastructure ensures smooth streaming with minimal buffering.",
            confidence: 0.9
        },
        'how quickly start': {
            answer: "Very quickly! Once you contact us via Telegram and complete payment, you'll receive your login details within 5-15 minutes and can start streaming immediately.",
            confidence: 0.9,
            actions: [
                { text: 'Get Started Now', type: 'telegram' }
            ]
        },
        'price': {
            answer: "We have 3 plans: Essential ($25/month, 2 connections), Premium ($30/month, 4 connections), and Elite ($35/month, 5 connections). All plans include premium content and 24/7 support.",
            confidence: 0.9
        },
        'reseller': {
            answer: "Yes! We offer reseller packages: Starter ($100 for 50 credits), Professional ($200 for 100 credits), and Enterprise ($700 for 500 credits). Great profit margins and full support included!",
            confidence: 0.9,
            actions: [
                { text: 'Learn More', type: 'telegram' }
            ]
        },
        'contact': {
            answer: "You can reach us through multiple channels: WhatsApp Group, Telegram, Phone (******-423-2975), or Facebook. We're available 24/7 to help!",
            confidence: 0.9,
            actions: [
                { text: 'WhatsApp Group', type: 'whatsapp' },
                { text: 'Call Us', type: 'phone' },
                { text: 'Telegram', type: 'telegram' }
            ]
        },
        'whatsapp': {
            answer: "Join our WhatsApp group for instant support and updates! Our team is active 24/7 to help with any questions or issues.",
            confidence: 0.9,
            actions: [
                { text: 'Join WhatsApp', type: 'whatsapp' }
            ]
        },
        'phone number': {
            answer: "You can call us directly at +****************. Our support team is available 24/7 to assist you with subscriptions, technical issues, and general inquiries.",
            confidence: 0.9,
            actions: [
                { text: 'Call Now', type: 'phone' },
                { text: 'WhatsApp', type: 'whatsapp' }
            ]
        }
    };
}

// Process user message and find best response
function processUserMessage(message) {
    const normalizedMessage = message.toLowerCase();

    // Check for greetings
    if (normalizedMessage.match(/^(hi|hello|hey|good morning|good afternoon|good evening)/)) {
        return {
            text: "Hello! Welcome to BadboyzTv. I'm here to help you with any questions about our streaming service. What would you like to know?",
            actions: [
                { text: 'View FAQ', type: 'faq' },
                { text: 'Contact Support', type: 'telegram' }
            ]
        };
    }

    // Check for thanks
    if (normalizedMessage.match(/(thank|thanks|appreciate)/)) {
        return {
            text: "You're welcome! Is there anything else I can help you with about BadboyzTv?",
            actions: [
                { text: 'Contact on Telegram', type: 'telegram' }
            ]
        };
    }

    // Search FAQ database
    let bestMatch = null;
    let highestScore = 0;

    for (const [key, value] of Object.entries(window.faqDatabase)) {
        const score = calculateSimilarity(normalizedMessage, key);
        if (score > highestScore && score > 0.3) {
            highestScore = score;
            bestMatch = value;
        }
    }

    if (bestMatch && highestScore > 0.5) {
        return {
            text: bestMatch.answer,
            actions: bestMatch.actions || [{ text: 'Contact on Telegram', type: 'telegram' }]
        };
    }

    // Default response for unmatched queries
    return {
        text: "I'd be happy to help you with that! For detailed assistance with your specific question, please contact our support team on Telegram where they can provide personalized help.",
        actions: [
            { text: 'Contact Support', type: 'telegram' },
            { text: 'View FAQ', type: 'faq' }
        ]
    };
}

// Calculate similarity between user message and FAQ keywords
function calculateSimilarity(message, keywords) {
    const messageWords = message.split(' ');
    const keywordWords = keywords.split(' ');
    let matches = 0;

    keywordWords.forEach(keyword => {
        messageWords.forEach(word => {
            if (word.includes(keyword) || keyword.includes(word)) {
                matches++;
            }
        });
    });

    return matches / Math.max(messageWords.length, keywordWords.length);
}
