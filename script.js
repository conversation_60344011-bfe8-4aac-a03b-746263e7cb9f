// DOM Content Loaded
document.addEventListener('DOMContentLoaded', function() {
    // Navigation functionality
    initNavigation();
    
    // Chat widget functionality
    initChatWidget();
    
    // Subscription buttons
    initSubscriptionButtons();
    
    // Smooth scrolling for anchor links
    initSmoothScrolling();
    
    // Intersection Observer for animations
    initScrollAnimations();

    // Initialize streaming bubbles
    initStreamingBubbles();

    // Initialize video background
    initVideoBackground();
});

// Navigation Functions
function initNavigation() {
    const navToggle = document.querySelector('.nav-toggle');
    const navMenu = document.querySelector('.nav-menu');
    const navLinks = document.querySelectorAll('.nav-link');

    // Mobile menu toggle
    if (navToggle) {
        navToggle.addEventListener('click', function() {
            navMenu.classList.toggle('active');
            
            // Animate hamburger menu
            const spans = navToggle.querySelectorAll('span');
            spans.forEach((span, index) => {
                span.style.transform = navMenu.classList.contains('active') 
                    ? `rotate(${index === 0 ? '45deg' : index === 1 ? '0deg' : '-45deg'})` 
                    : 'rotate(0deg)';
                if (index === 1) {
                    span.style.opacity = navMenu.classList.contains('active') ? '0' : '1';
                }
            });
        });
    }

    // Close mobile menu when clicking on links
    navLinks.forEach(link => {
        link.addEventListener('click', () => {
            navMenu.classList.remove('active');
            const spans = navToggle.querySelectorAll('span');
            spans.forEach((span, index) => {
                span.style.transform = 'rotate(0deg)';
                if (index === 1) span.style.opacity = '1';
            });
        });
    });

    // Navbar scroll effect
    window.addEventListener('scroll', function() {
        const navbar = document.querySelector('.navbar');
        if (window.scrollY > 100) {
            navbar.style.background = 'rgba(10, 10, 10, 0.98)';
            navbar.style.backdropFilter = 'blur(20px)';
        } else {
            navbar.style.background = 'rgba(10, 10, 10, 0.95)';
        }
    });
}

// Chat Widget Functions
function initChatWidget() {
    const chatToggle = document.getElementById('chat-toggle');
    const chatWindow = document.getElementById('chat-window');
    const chatClose = document.getElementById('chat-close');
    const chatInput = document.getElementById('chat-input');
    const chatSend = document.getElementById('chat-send');
    const chatBody = document.querySelector('.chat-body');

    // Toggle chat window
    chatToggle.addEventListener('click', function() {
        chatWindow.classList.toggle('active');
        if (chatWindow.classList.contains('active')) {
            chatInput.focus();
        }
    });

    // Close chat window
    chatClose.addEventListener('click', function() {
        chatWindow.classList.remove('active');
    });

    // Send message functionality
    function sendMessage() {
        const message = chatInput.value.trim();
        if (message) {
            // Add user message
            addMessage(message, 'user');
            chatInput.value = '';
            
            // Simulate bot response
            setTimeout(() => {
                const responses = [
                    "Thank you for your message! Our team will get back to you shortly.",
                    "For immediate assistance, please join our Telegram group: https://t.me/badboyztbv",
                    "We're here to help! What specific information do you need about our services?",
                    "Great question! Let me connect you with our support team for detailed assistance."
                ];
                const randomResponse = responses[Math.floor(Math.random() * responses.length)];
                addMessage(randomResponse, 'bot');
            }, 1000);
        }
    }

    // Add message to chat
    function addMessage(text, sender) {
        const messageDiv = document.createElement('div');
        messageDiv.className = `chat-message ${sender}`;
        messageDiv.innerHTML = `<p>${text}</p>`;
        chatBody.appendChild(messageDiv);
        chatBody.scrollTop = chatBody.scrollHeight;
    }

    // Send message on button click
    chatSend.addEventListener('click', sendMessage);

    // Send message on Enter key
    chatInput.addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            sendMessage();
        }
    });
}

// Subscription Button Functions
function initSubscriptionButtons() {
    const subscribeButtons = document.querySelectorAll('.subscribe-btn');
    const resellerButtons = document.querySelectorAll('.reseller-btn');
    
    // Handle subscription buttons (redirect to Telegram)
    subscribeButtons.forEach(button => {
        button.addEventListener('click', function() {
            const plan = this.getAttribute('data-plan');
            
            // Add loading state
            const originalText = this.innerHTML;
            this.innerHTML = '<span class="loading"></span> Redirecting...';
            this.disabled = true;
            
            // Simulate processing time then redirect
            setTimeout(() => {
                window.open('https://t.me/badboyztbv', '_blank');
                
                // Reset button
                this.innerHTML = originalText;
                this.disabled = false;
            }, 1500);
        });
    });

    // Handle reseller buttons (redirect to Telegram)
    resellerButtons.forEach(button => {
        button.addEventListener('click', function() {
            const package = this.getAttribute('data-package');
            
            // Add loading state
            const originalText = this.innerHTML;
            this.innerHTML = '<span class="loading"></span> Redirecting...';
            this.disabled = true;
            
            // Simulate processing time then redirect
            setTimeout(() => {
                window.open('https://t.me/badboyztbv', '_blank');
                
                // Reset button
                this.innerHTML = originalText;
                this.disabled = false;
            }, 1500);
        });
    });
}

// Smooth Scrolling Functions
function initSmoothScrolling() {
    const links = document.querySelectorAll('a[href^="#"]');
    
    links.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            
            const targetId = this.getAttribute('href');
            const targetSection = document.querySelector(targetId);
            
            if (targetSection) {
                const offsetTop = targetSection.offsetTop - 80; // Account for fixed navbar
                
                window.scrollTo({
                    top: offsetTop,
                    behavior: 'smooth'
                });
            }
        });
    });
}

// Scroll Animations
function initScrollAnimations() {
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };

    const observer = new IntersectionObserver(function(entries) {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('fade-in-up');
                observer.unobserve(entry.target);
            }
        });
    }, observerOptions);

    // Observe elements for animation
    const animateElements = document.querySelectorAll('.feature-card, .pricing-card, .benefit-card, .step');
    animateElements.forEach(el => {
        observer.observe(el);
    });
}

// Utility Functions
function showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.className = `notification ${type}`;
    notification.innerHTML = `
        <div class="notification-content">
            <span>${message}</span>
            <button class="notification-close">&times;</button>
        </div>
    `;
    
    document.body.appendChild(notification);
    
    // Auto remove after 5 seconds
    setTimeout(() => {
        notification.remove();
    }, 5000);
    
    // Manual close
    notification.querySelector('.notification-close').addEventListener('click', () => {
        notification.remove();
    });
}

// Floating cards animation enhancement
function enhanceFloatingCards() {
    const cards = document.querySelectorAll('.floating-card');
    
    cards.forEach((card, index) => {
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-10px) scale(1.05)';
            this.style.boxShadow = '0 20px 40px rgba(212, 175, 55, 0.3)';
        });
        
        card.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0) scale(1)';
            this.style.boxShadow = 'none';
        });
    });
}

// Initialize floating cards enhancement
document.addEventListener('DOMContentLoaded', enhanceFloatingCards);

// Pricing card hover effects
function enhancePricingCards() {
    const pricingCards = document.querySelectorAll('.pricing-card');
    
    pricingCards.forEach(card => {
        card.addEventListener('mouseenter', function() {
            if (!this.classList.contains('featured')) {
                this.style.transform = 'translateY(-10px)';
                this.style.boxShadow = '0 20px 40px rgba(212, 175, 55, 0.2)';
            }
        });
        
        card.addEventListener('mouseleave', function() {
            if (!this.classList.contains('featured')) {
                this.style.transform = 'translateY(0)';
                this.style.boxShadow = 'none';
            }
        });
    });
}

// Initialize pricing cards enhancement
document.addEventListener('DOMContentLoaded', enhancePricingCards);

// Performance optimization - Lazy loading for images
function initLazyLoading() {
    const images = document.querySelectorAll('img[data-src]');
    
    const imageObserver = new IntersectionObserver((entries, observer) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                const img = entry.target;
                img.src = img.dataset.src;
                img.classList.remove('lazy');
                imageObserver.unobserve(img);
            }
        });
    });
    
    images.forEach(img => imageObserver.observe(img));
}

// Initialize lazy loading
document.addEventListener('DOMContentLoaded', initLazyLoading);

// Streaming Bubbles Functions
function initStreamingBubbles() {
    const bubbles = document.querySelectorAll('.bubble');

    bubbles.forEach((bubble, index) => {
        // Add random animation variations
        const randomDelay = Math.random() * 2;
        const randomDuration = 6 + Math.random() * 3;

        bubble.style.animationDelay = randomDelay + 's';
        bubble.style.animationDuration = randomDuration + 's';

        // Add click interaction
        bubble.addEventListener('click', function(e) {
            e.stopPropagation();

            // Create ripple effect
            const ripple = document.createElement('div');
            ripple.className = 'bubble-ripple';
            this.appendChild(ripple);

            // Animate ripple
            setTimeout(() => {
                ripple.remove();
            }, 600);

            // Show platform info
            showPlatformInfo(this);
        });

        // Add hover effects
        bubble.addEventListener('mouseenter', function() {
            this.style.transform = 'scale(1.3)';
            this.style.borderColor = 'var(--primary-gold)';
            this.style.boxShadow = '0 0 25px rgba(212, 175, 55, 0.6)';
        });

        bubble.addEventListener('mouseleave', function() {
            this.style.transform = 'scale(1)';
            this.style.borderColor = 'rgba(212, 175, 55, 0.3)';
            this.style.boxShadow = 'none';
        });
    });

    // Add floating particles
    createFloatingParticles();
}

// Show platform information
function showPlatformInfo(bubble) {
    const platformNames = {
        'netflix': 'Netflix Content Available',
        'hulu': 'Hulu Shows & Movies',
        'disney': 'Disney+ Collection',
        'amazon': 'Prime Video Library',
        'hbo': 'HBO Max Originals',
        'apple': 'Apple TV+ Exclusives',
        'paramount': 'Paramount+ Content',
        'peacock': 'Peacock Streaming'
    };

    const className = Array.from(bubble.classList).find(cls => platformNames[cls]);
    const platformName = platformNames[className] || 'Premium Content';

    // Create floating text
    const floatingText = document.createElement('div');
    floatingText.className = 'floating-platform-text';
    floatingText.textContent = platformName;
    floatingText.style.position = 'absolute';
    floatingText.style.top = bubble.offsetTop - 30 + 'px';
    floatingText.style.left = bubble.offsetLeft + 'px';
    floatingText.style.color = 'var(--primary-gold)';
    floatingText.style.fontWeight = 'bold';
    floatingText.style.fontSize = '14px';
    floatingText.style.zIndex = '10';
    floatingText.style.pointerEvents = 'none';
    floatingText.style.animation = 'fadeInUp 0.5s ease-out';

    bubble.parentElement.appendChild(floatingText);

    setTimeout(() => {
        floatingText.remove();
    }, 2000);
}

// Create floating particles
function createFloatingParticles() {
    const heroSection = document.querySelector('.hero');
    if (!heroSection) return;

    for (let i = 0; i < 20; i++) {
        const particle = document.createElement('div');
        particle.className = 'floating-particle';
        particle.style.position = 'absolute';
        particle.style.width = Math.random() * 4 + 2 + 'px';
        particle.style.height = particle.style.width;
        particle.style.background = 'rgba(212, 175, 55, 0.3)';
        particle.style.borderRadius = '50%';
        particle.style.left = Math.random() * 100 + '%';
        particle.style.top = Math.random() * 100 + '%';
        particle.style.animation = `particle-float ${8 + Math.random() * 4}s infinite ease-in-out`;
        particle.style.animationDelay = Math.random() * 5 + 's';
        particle.style.zIndex = '1';

        heroSection.appendChild(particle);
    }
}

// Video Background Functions
function initVideoBackground() {
    const videos = document.querySelectorAll('.hero-video video');

    videos.forEach(video => {
        // Ensure video plays on mobile
        video.addEventListener('loadeddata', function() {
            this.play().catch(e => {
                console.log('Video autoplay prevented:', e);
                // Fallback: show static background
                this.style.display = 'none';
                this.parentElement.style.background = 'var(--gradient-dark)';
            });
        });

        // Handle video errors
        video.addEventListener('error', function() {
            console.log('Video failed to load, using fallback background');
            this.style.display = 'none';
            this.parentElement.style.background = 'var(--gradient-dark)';
        });

        // Optimize video performance
        video.addEventListener('canplay', function() {
            this.style.opacity = '0.3';
        });
    });
}

// Enhanced bubble animations
function enhanceBubbleAnimations() {
    const bubbles = document.querySelectorAll('.bubble');

    setInterval(() => {
        bubbles.forEach(bubble => {
            // Random pulse effect
            if (Math.random() < 0.1) {
                bubble.style.animation = 'bubble-pulse 1s ease-in-out';
                setTimeout(() => {
                    bubble.style.animation = '';
                }, 1000);
            }
        });
    }, 3000);
}

// Initialize enhanced animations
document.addEventListener('DOMContentLoaded', enhanceBubbleAnimations);
