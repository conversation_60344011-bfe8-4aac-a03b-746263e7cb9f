/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    --primary-gold: #D4AF37;
    --secondary-gold: #B8860B;
    --dark-bg: #0A0A0A;
    --darker-bg: #050505;
    --card-bg: #1A1A1A;
    --text-primary: #FFFFFF;
    --text-secondary: #B8B8B8;
    --text-muted: #888888;
    --border-color: #333333;
    --gradient-primary: linear-gradient(135deg, #D4AF37 0%, #B8860B 100%);
    --gradient-dark: linear-gradient(135deg, #1A1A1A 0%, #0A0A0A 100%);
    --shadow-gold: 0 0 30px rgba(212, 175, 55, 0.3);
    --shadow-dark: 0 10px 40px rgba(0, 0, 0, 0.5);
}

body {
    font-family: 'Inter', sans-serif;
    background: var(--dark-bg);
    color: var(--text-primary);
    line-height: 1.6;
    overflow-x: hidden;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
    font-family: 'Playfair Display', serif;
    font-weight: 600;
    line-height: 1.2;
}

.accent {
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

/* Navigation */
.navbar {
    position: fixed;
    top: 0;
    width: 100%;
    background: rgba(10, 10, 10, 0.95);
    backdrop-filter: blur(20px);
    border-bottom: 1px solid var(--border-color);
    z-index: 1000;
    transition: all 0.3s ease;
}

.nav-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 2rem;
    max-width: 1200px;
    margin: 0 auto;
}

.nav-logo h1 {
    font-size: 1.8rem;
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin-bottom: 0;
}

.nav-logo .tagline {
    font-size: 0.75rem;
    color: var(--text-muted);
    text-transform: uppercase;
    letter-spacing: 1px;
}

.nav-menu {
    display: flex;
    list-style: none;
    gap: 2rem;
}

.nav-link {
    color: var(--text-secondary);
    text-decoration: none;
    font-weight: 500;
    transition: all 0.3s ease;
    position: relative;
}

.nav-link:hover,
.nav-link.active {
    color: var(--primary-gold);
}

.nav-link::after {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 0;
    width: 0;
    height: 2px;
    background: var(--gradient-primary);
    transition: width 0.3s ease;
}

.nav-link:hover::after,
.nav-link.active::after {
    width: 100%;
}

.nav-toggle {
    display: none;
    flex-direction: column;
    cursor: pointer;
}

.nav-toggle span {
    width: 25px;
    height: 3px;
    background: var(--primary-gold);
    margin: 3px 0;
    transition: 0.3s;
}

/* Hero Section */
.hero {
    min-height: 100vh;
    display: flex;
    align-items: center;
    background: var(--gradient-dark);
    position: relative;
    overflow: hidden;
}

/* Background Video */
.hero-video {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1;
}

.hero-video video {
    width: 100%;
    height: 100%;
    object-fit: cover;
    opacity: 0.3;
}

/* Animated Background Fallback */
.animated-bg {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(45deg,
        rgba(10, 10, 10, 0.9) 0%,
        rgba(26, 26, 26, 0.8) 25%,
        rgba(15, 15, 15, 0.9) 50%,
        rgba(20, 20, 20, 0.8) 75%,
        rgba(10, 10, 10, 0.9) 100%);
    background-size: 400% 400%;
    animation: gradient-shift 15s ease infinite;
    opacity: 0;
    transition: opacity 2s ease;
}

.hero-video video:not([src]),
.hero-video video[src=""] {
    display: none;
}

.hero-video video:not([src]) + .video-overlay + .animated-bg,
.hero-video video[src=""] + .video-overlay + .animated-bg {
    opacity: 1;
}

@keyframes gradient-shift {
    0% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
    100% { background-position: 0% 50%; }
}

.video-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg,
        rgba(10, 10, 10, 0.8) 0%,
        rgba(26, 26, 26, 0.7) 50%,
        rgba(10, 10, 10, 0.9) 100%);
    z-index: 2;
}

.hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(circle at 30% 50%, rgba(212, 175, 55, 0.1) 0%, transparent 50%);
    z-index: 3;
}

/* Streaming Bubbles */
.streaming-bubbles {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 2;
    pointer-events: none;
}

.bubble {
    position: absolute;
    width: 60px;
    height: 60px;
    background: rgba(255, 255, 255, 0.1);
    border: 2px solid rgba(212, 175, 55, 0.3);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    backdrop-filter: blur(10px);
    animation: bubble-float 8s infinite ease-in-out;
    opacity: 0.8;
}

.platform-text {
    font-size: 9px;
    font-weight: 800;
    text-align: center;
    letter-spacing: 0.8px;
    color: #FFFFFF;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.8);
    font-family: 'Inter', sans-serif;
    line-height: 1;
    display: block;
    padding: 2px;
}

/* Individual bubble positions, delays, and brand colors */
.bubble.netflix {
    top: 15%;
    left: 10%;
    animation-delay: 0s;
    animation-duration: 6s;
    background: linear-gradient(135deg, rgba(229, 9, 20, 0.2) 0%, rgba(139, 0, 0, 0.3) 100%);
    border-color: rgba(229, 9, 20, 0.5);
}

.bubble.hulu {
    top: 25%;
    right: 15%;
    animation-delay: 1s;
    animation-duration: 7s;
    background: linear-gradient(135deg, rgba(28, 231, 131, 0.2) 0%, rgba(0, 139, 69, 0.3) 100%);
    border-color: rgba(28, 231, 131, 0.5);
}

.bubble.disney {
    top: 45%;
    left: 5%;
    animation-delay: 2s;
    animation-duration: 8s;
    background: linear-gradient(135deg, rgba(17, 60, 207, 0.2) 0%, rgba(0, 30, 139, 0.3) 100%);
    border-color: rgba(17, 60, 207, 0.5);
}

.bubble.amazon {
    top: 35%;
    right: 8%;
    animation-delay: 3s;
    animation-duration: 6.5s;
    background: linear-gradient(135deg, rgba(0, 168, 225, 0.2) 0%, rgba(0, 100, 139, 0.3) 100%);
    border-color: rgba(0, 168, 225, 0.5);
}

.bubble.hbo {
    bottom: 30%;
    left: 12%;
    animation-delay: 4s;
    animation-duration: 7.5s;
    background: linear-gradient(135deg, rgba(150, 39, 224, 0.2) 0%, rgba(75, 0, 139, 0.3) 100%);
    border-color: rgba(150, 39, 224, 0.5);
}

.bubble.apple {
    bottom: 20%;
    right: 20%;
    animation-delay: 5s;
    animation-duration: 6s;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.2) 0%, rgba(139, 139, 139, 0.3) 100%);
    border-color: rgba(255, 255, 255, 0.5);
}

.bubble.paramount {
    top: 60%;
    left: 25%;
    animation-delay: 1.5s;
    animation-duration: 8.5s;
    background: linear-gradient(135deg, rgba(0, 101, 239, 0.2) 0%, rgba(0, 50, 139, 0.3) 100%);
    border-color: rgba(0, 101, 239, 0.5);
}

.bubble.peacock {
    bottom: 40%;
    right: 35%;
    animation-delay: 2.5s;
    animation-duration: 7s;
    background: linear-gradient(135deg, rgba(0, 0, 0, 0.2) 0%, rgba(139, 139, 139, 0.3) 100%);
    border-color: rgba(255, 255, 255, 0.5);
}

/* Bubble animation */
@keyframes bubble-float {
    0%, 100% {
        transform: translateY(0px) translateX(0px) scale(1);
        opacity: 0.6;
    }
    25% {
        transform: translateY(-20px) translateX(10px) scale(1.1);
        opacity: 0.8;
    }
    50% {
        transform: translateY(-10px) translateX(-15px) scale(0.9);
        opacity: 1;
    }
    75% {
        transform: translateY(-25px) translateX(5px) scale(1.05);
        opacity: 0.7;
    }
}

/* Bubble hover effect */
.bubble:hover {
    transform: scale(1.2) !important;
    border-color: var(--primary-gold);
    box-shadow: 0 0 20px rgba(212, 175, 55, 0.5);
    transition: all 0.3s ease;
}

/* Reseller bubbles - different positioning */
.reseller-bubbles .bubble.netflix {
    top: 20%;
    left: 8%;
}

.reseller-bubbles .bubble.hulu {
    top: 40%;
    right: 10%;
}

.reseller-bubbles .bubble.disney {
    bottom: 35%;
    left: 15%;
}

.reseller-bubbles .bubble.amazon {
    top: 30%;
    left: 30%;
}

.reseller-bubbles .bubble.hbo {
    bottom: 25%;
    right: 25%;
}

.reseller-bubbles .bubble.apple {
    top: 55%;
    right: 5%;
}

.hero-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: center;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 2rem;
    position: relative;
    z-index: 4;
}

.hero-title {
    font-size: 3.5rem;
    margin-bottom: 1.5rem;
    line-height: 1.1;
}

.hero-subtitle {
    font-size: 1.2rem;
    color: var(--text-secondary);
    margin-bottom: 2.5rem;
    line-height: 1.6;
}

.hero-buttons {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
}

/* Buttons */
.btn {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 1rem 2rem;
    border: none;
    border-radius: 50px;
    font-weight: 600;
    text-decoration: none;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 1rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.btn-primary {
    background: var(--gradient-primary);
    color: var(--dark-bg);
    box-shadow: var(--shadow-gold);
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 0 40px rgba(212, 175, 55, 0.5);
}

.btn-secondary {
    background: transparent;
    color: var(--text-primary);
    border: 2px solid var(--border-color);
}

.btn-secondary:hover {
    border-color: var(--primary-gold);
    color: var(--primary-gold);
    transform: translateY(-2px);
}

.btn-outline {
    background: transparent;
    color: var(--primary-gold);
    border: 2px solid var(--primary-gold);
}

.btn-outline:hover {
    background: var(--primary-gold);
    color: var(--dark-bg);
    transform: translateY(-2px);
}

.btn-large {
    padding: 1.25rem 2.5rem;
    font-size: 1.1rem;
}

/* Hero Visual Elements */
.hero-visual {
    position: relative;
    height: 500px;
}

.floating-card {
    position: absolute;
    background: var(--card-bg);
    border: 1px solid var(--border-color);
    border-radius: 20px;
    padding: 2rem;
    backdrop-filter: blur(20px);
    animation: float 6s ease-in-out infinite;
}

.floating-card:nth-child(1) {
    top: 20%;
    left: 10%;
    animation-delay: 0s;
}

.floating-card:nth-child(2) {
    top: 50%;
    right: 20%;
    animation-delay: 2s;
}

.floating-card:nth-child(3) {
    bottom: 20%;
    left: 30%;
    animation-delay: 4s;
}

.floating-card .card-content i {
    font-size: 2rem;
    color: var(--primary-gold);
    margin-bottom: 1rem;
}

.floating-card h3 {
    font-size: 1.2rem;
    margin-bottom: 0.5rem;
}

.floating-card p {
    color: var(--text-secondary);
    font-size: 0.9rem;
}

@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-20px); }
}

/* Section Styles */
section {
    padding: 6rem 0;
}

.section-header {
    text-align: center;
    margin-bottom: 4rem;
}

.section-header h2 {
    font-size: 2.5rem;
    margin-bottom: 1rem;
}

.section-header p {
    font-size: 1.1rem;
    color: var(--text-secondary);
    max-width: 600px;
    margin: 0 auto;
}

/* Features Section */
.features {
    background: var(--darker-bg);
}

.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 2rem;
}

.feature-card {
    background: var(--card-bg);
    border: 1px solid var(--border-color);
    border-radius: 20px;
    padding: 2.5rem;
    text-align: center;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.feature-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(212, 175, 55, 0.1), transparent);
    transition: left 0.5s ease;
}

.feature-card:hover::before {
    left: 100%;
}

.feature-card:hover {
    transform: translateY(-10px);
    border-color: var(--primary-gold);
    box-shadow: var(--shadow-gold);
}

.feature-icon {
    width: 80px;
    height: 80px;
    background: var(--gradient-primary);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
}

.feature-icon i {
    font-size: 2rem;
    color: var(--dark-bg);
}

.feature-card h3 {
    font-size: 1.5rem;
    margin-bottom: 1rem;
}

.feature-card p {
    color: var(--text-secondary);
    line-height: 1.6;
}

/* Pricing Section */
.pricing {
    background: var(--dark-bg);
}

.pricing-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2rem;
    max-width: 1100px;
    margin: 0 auto;
}

.pricing-card {
    background: var(--card-bg);
    border: 2px solid var(--border-color);
    border-radius: 25px;
    padding: 2.5rem;
    text-align: center;
    position: relative;
    transition: all 0.3s ease;
    overflow: hidden;
}

.pricing-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--gradient-primary);
    transform: scaleX(0);
    transition: transform 0.3s ease;
}

.pricing-card:hover::before {
    transform: scaleX(1);
}

.pricing-card.featured {
    border-color: var(--primary-gold);
    transform: scale(1.05);
    box-shadow: var(--shadow-gold);
}

.pricing-card.enterprise {
    border-color: var(--secondary-gold);
    background: linear-gradient(135deg, var(--card-bg) 0%, rgba(212, 175, 55, 0.05) 100%);
}

.popular-badge,
.enterprise-badge {
    position: absolute;
    top: -1px;
    left: 50%;
    transform: translateX(-50%);
    background: var(--gradient-primary);
    color: var(--dark-bg);
    padding: 0.5rem 1.5rem;
    border-radius: 0 0 15px 15px;
    font-weight: 600;
    font-size: 0.9rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.enterprise-badge {
    background: linear-gradient(135deg, #B8860B 0%, #8B6914 100%);
}

.pricing-header h3 {
    font-size: 1.8rem;
    margin-bottom: 1rem;
    color: var(--primary-gold);
}

.price {
    display: flex;
    align-items: baseline;
    justify-content: center;
    margin-bottom: 1rem;
}

.currency {
    font-size: 1.5rem;
    color: var(--text-secondary);
}

.amount {
    font-size: 3.5rem;
    font-weight: 700;
    color: var(--text-primary);
    margin: 0 0.25rem;
}

.period {
    font-size: 1.2rem;
    color: var(--text-secondary);
}

.credits {
    font-size: 1.1rem;
    color: var(--primary-gold);
    font-weight: 600;
    margin-bottom: 2rem;
}

.pricing-features {
    margin: 2rem 0;
}

.pricing-features ul {
    list-style: none;
    text-align: left;
}

.pricing-features li {
    display: flex;
    align-items: center;
    margin-bottom: 1rem;
    color: var(--text-secondary);
}

.pricing-features i {
    color: var(--primary-gold);
    margin-right: 1rem;
    font-size: 1.1rem;
}

/* Reseller Specific Styles */
.reseller-hero {
    background: linear-gradient(135deg, var(--darker-bg) 0%, var(--dark-bg) 100%);
}

.hero-stats {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 2rem;
    margin-top: 3rem;
}

.stat {
    text-align: center;
    padding: 1.5rem;
    background: var(--card-bg);
    border-radius: 15px;
    border: 1px solid var(--border-color);
}

.stat h3 {
    font-size: 2.5rem;
    color: var(--primary-gold);
    margin-bottom: 0.5rem;
}

.stat p {
    color: var(--text-secondary);
    font-size: 0.9rem;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.benefits {
    background: var(--darker-bg);
}

.benefits-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 2rem;
}

.benefit-card {
    background: var(--card-bg);
    border: 1px solid var(--border-color);
    border-radius: 20px;
    padding: 2rem;
    text-align: center;
    transition: all 0.3s ease;
}

.benefit-card:hover {
    transform: translateY(-5px);
    border-color: var(--primary-gold);
    box-shadow: 0 10px 30px rgba(212, 175, 55, 0.2);
}

.benefit-icon {
    width: 70px;
    height: 70px;
    background: var(--gradient-primary);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
}

.benefit-icon i {
    font-size: 1.8rem;
    color: var(--dark-bg);
}

.benefit-card h3 {
    font-size: 1.3rem;
    margin-bottom: 1rem;
}

.benefit-card p {
    color: var(--text-secondary);
    line-height: 1.6;
}

/* How It Works Section */
.how-it-works {
    background: var(--dark-bg);
}

.steps-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
}

.step {
    text-align: center;
    padding: 2rem;
    position: relative;
}

.step-number {
    width: 60px;
    height: 60px;
    background: var(--gradient-primary);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--dark-bg);
    margin: 0 auto 1.5rem;
}

.step h3 {
    font-size: 1.3rem;
    margin-bottom: 1rem;
}

.step p {
    color: var(--text-secondary);
    line-height: 1.6;
}

/* CTA Section */
.cta-section {
    background: var(--gradient-dark);
    text-align: center;
    position: relative;
}

.cta-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(circle at center, rgba(212, 175, 55, 0.1) 0%, transparent 70%);
}

.cta-content {
    position: relative;
    z-index: 2;
}

.cta-content h2 {
    font-size: 2.5rem;
    margin-bottom: 1rem;
}

.cta-content p {
    font-size: 1.2rem;
    color: var(--text-secondary);
    margin-bottom: 2rem;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

/* Footer */
.footer {
    background: var(--darker-bg);
    border-top: 1px solid var(--border-color);
    padding: 3rem 0 1rem;
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    margin-bottom: 2rem;
}

.footer-section h3,
.footer-section h4 {
    color: var(--primary-gold);
    margin-bottom: 1rem;
}

.footer-section p {
    color: var(--text-secondary);
    line-height: 1.6;
}

.footer-section ul {
    list-style: none;
}

.footer-section ul li {
    margin-bottom: 0.5rem;
}

.footer-section ul li a {
    color: var(--text-secondary);
    text-decoration: none;
    transition: color 0.3s ease;
}

.footer-section ul li a:hover {
    color: var(--primary-gold);
}

.footer-bottom {
    text-align: center;
    padding-top: 2rem;
    border-top: 1px solid var(--border-color);
    color: var(--text-muted);
}

/* Live Chat Widget */
.chat-widget {
    position: fixed;
    bottom: 20px;
    right: 20px;
    z-index: 1000;
}

.chat-toggle {
    width: 60px;
    height: 60px;
    background: var(--gradient-primary);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    box-shadow: var(--shadow-gold);
    transition: all 0.3s ease;
}

.chat-toggle:hover {
    transform: scale(1.1);
    box-shadow: 0 0 40px rgba(212, 175, 55, 0.5);
}

.chat-toggle i {
    font-size: 1.5rem;
    color: var(--dark-bg);
}

.chat-window {
    position: absolute;
    bottom: 80px;
    right: 0;
    width: 350px;
    height: 450px;
    background: var(--card-bg);
    border: 1px solid var(--border-color);
    border-radius: 20px;
    display: none;
    flex-direction: column;
    box-shadow: var(--shadow-dark);
}

.chat-window.active {
    display: flex;
}

.chat-header {
    background: var(--gradient-primary);
    color: var(--dark-bg);
    padding: 1rem;
    border-radius: 20px 20px 0 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.chat-header h4 {
    margin: 0;
    font-size: 1.1rem;
}

.chat-close {
    background: none;
    border: none;
    color: var(--dark-bg);
    cursor: pointer;
    font-size: 1.2rem;
}

.chat-body {
    flex: 1;
    padding: 1rem;
    overflow-y: auto;
}

.chat-message {
    margin-bottom: 1rem;
    padding: 0.75rem;
    border-radius: 15px;
    max-width: 80%;
}

.chat-message.bot {
    background: var(--darker-bg);
    color: var(--text-primary);
    margin-right: auto;
}

.chat-message.user {
    background: var(--primary-gold);
    color: var(--dark-bg);
    margin-left: auto;
}

.chat-input {
    display: flex;
    padding: 1rem;
    border-top: 1px solid var(--border-color);
}

.chat-input input {
    flex: 1;
    background: var(--darker-bg);
    border: 1px solid var(--border-color);
    border-radius: 25px;
    padding: 0.75rem 1rem;
    color: var(--text-primary);
    margin-right: 0.5rem;
}

.chat-input input:focus {
    outline: none;
    border-color: var(--primary-gold);
}

.chat-input button {
    background: var(--gradient-primary);
    border: none;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    color: var(--dark-bg);
}

/* Responsive Design */
@media (max-width: 768px) {
    .nav-menu {
        position: fixed;
        left: -100%;
        top: 70px;
        flex-direction: column;
        background: var(--card-bg);
        width: 100%;
        text-align: center;
        transition: 0.3s;
        box-shadow: var(--shadow-dark);
        padding: 2rem 0;
    }

    .nav-menu.active {
        left: 0;
    }

    .nav-toggle {
        display: flex;
    }

    .hero-content {
        grid-template-columns: 1fr;
        text-align: center;
        gap: 2rem;
    }

    .hero-title {
        font-size: 2.5rem;
    }

    .hero-visual {
        height: 300px;
    }

    .floating-card {
        position: relative;
        margin-bottom: 1rem;
        animation: none;
    }

    .floating-card:nth-child(2),
    .floating-card:nth-child(3) {
        position: relative;
        top: auto;
        left: auto;
        right: auto;
        bottom: auto;
    }

    .hero-stats {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .pricing-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }

    .pricing-card.featured {
        transform: none;
    }

    .section-header h2 {
        font-size: 2rem;
    }

    .hero-buttons {
        justify-content: center;
    }

    .btn {
        padding: 0.875rem 1.5rem;
        font-size: 0.9rem;
    }

    .chat-window {
        width: 300px;
        height: 400px;
    }

    .container {
        padding: 0 15px;
    }

    section {
        padding: 4rem 0;
    }

    /* Mobile Video and Bubbles */
    .hero-video video {
        opacity: 0.2;
    }

    .bubble {
        width: 45px;
        height: 45px;
    }

    .bubble img {
        width: 25px;
    }

    /* Reduce particles on mobile */
    .floating-particle {
        display: none;
    }
}

@media (max-width: 480px) {
    .hero-title {
        font-size: 2rem;
    }

    .hero-subtitle {
        font-size: 1rem;
    }

    .nav-container {
        padding: 1rem;
    }

    .pricing-card,
    .feature-card,
    .benefit-card {
        padding: 1.5rem;
    }

    .chat-window {
        width: 280px;
        height: 350px;
        bottom: 70px;
        right: -10px;
    }

    .amount {
        font-size: 2.5rem;
    }

    .btn {
        padding: 0.75rem 1.25rem;
        font-size: 0.85rem;
    }
}

/* Animations and Transitions */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in-up {
    animation: fadeInUp 0.6s ease-out;
}

/* Smooth Scrolling */
html {
    scroll-behavior: smooth;
}

/* Custom Scrollbar */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: var(--darker-bg);
}

::-webkit-scrollbar-thumb {
    background: var(--primary-gold);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: var(--secondary-gold);
}

/* Loading Animation */
.loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid var(--border-color);
    border-radius: 50%;
    border-top-color: var(--primary-gold);
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* Particle Animations */
@keyframes particle-float {
    0%, 100% {
        transform: translateY(0px) translateX(0px);
        opacity: 0.3;
    }
    25% {
        transform: translateY(-30px) translateX(20px);
        opacity: 0.7;
    }
    50% {
        transform: translateY(-15px) translateX(-25px);
        opacity: 1;
    }
    75% {
        transform: translateY(-40px) translateX(10px);
        opacity: 0.5;
    }
}

/* Bubble Ripple Effect */
.bubble-ripple {
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    background: rgba(212, 175, 55, 0.4);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    animation: ripple-expand 0.6s ease-out;
    pointer-events: none;
}

@keyframes ripple-expand {
    0% {
        width: 0;
        height: 0;
        opacity: 1;
    }
    100% {
        width: 120px;
        height: 120px;
        opacity: 0;
    }
}

/* Bubble Pulse Animation */
@keyframes bubble-pulse {
    0%, 100% {
        transform: scale(1);
        box-shadow: 0 0 0 0 rgba(212, 175, 55, 0.4);
    }
    50% {
        transform: scale(1.1);
        box-shadow: 0 0 0 20px rgba(212, 175, 55, 0);
    }
}

/* Floating Platform Text */
.floating-platform-text {
    animation: platform-text-float 2s ease-out forwards;
}

@keyframes platform-text-float {
    0% {
        opacity: 0;
        transform: translateY(10px);
    }
    20% {
        opacity: 1;
        transform: translateY(-10px);
    }
    80% {
        opacity: 1;
        transform: translateY(-15px);
    }
    100% {
        opacity: 0;
        transform: translateY(-25px);
    }
}

/* Enhanced Video Overlay */
.video-overlay::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: radial-gradient(
        ellipse at center,
        transparent 0%,
        rgba(10, 10, 10, 0.3) 70%,
        rgba(10, 10, 10, 0.8) 100%
    );
}

/* Floating Particles */
.floating-particle {
    pointer-events: none;
    filter: blur(1px);
}

/* Enhanced Bubble Glow Effect */
.bubble::after {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    background: linear-gradient(45deg,
        rgba(212, 175, 55, 0.1) 0%,
        rgba(212, 175, 55, 0.3) 50%,
        rgba(212, 175, 55, 0.1) 100%);
    border-radius: 50%;
    z-index: -1;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.bubble:hover::after {
    opacity: 1;
    animation: glow-rotate 2s linear infinite;
}

@keyframes glow-rotate {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Utility Classes */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }
.mb-1 { margin-bottom: 1rem; }
.mb-2 { margin-bottom: 2rem; }
.mt-1 { margin-top: 1rem; }
.mt-2 { margin-top: 2rem; }
.hidden { display: none; }
.visible { display: block; }
