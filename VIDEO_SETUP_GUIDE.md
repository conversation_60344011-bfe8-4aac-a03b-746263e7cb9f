# 🎬 BadboyzTv Video Setup Guide

## How to Add Your Own Background Video

### Step 1: Prepare Your Video File

**Recommended Video Specifications:**
- **Format**: MP4 (H.264 codec)
- **Resolution**: 1920x1080 (Full HD) or higher
- **Duration**: 10-30 seconds (loops automatically)
- **File Size**: Under 10MB for best performance
- **Aspect Ratio**: 16:9 (widescreen)

**Video Content Suggestions:**
- Streaming/entertainment themed footage
- Abstract motion graphics
- Technology/server room footage
- Dark, cinematic scenes that won't distract from text

### Step 2: Add Your Video File

1. **Place your video file** in the same folder as your HTML files
2. **Name your video file** something simple like: `background-video.mp4`
3. **Update the HTML files** to use your video

### Step 3: Update the HTML Code

**For index.html (Home Page):**

Find this section around line 34:
```html
<video autoplay muted loop playsinline>
    <source src="https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/BigBuckBunny.mp4" type="video/mp4">
    <!-- Fallback for browsers that don't support video -->
</video>
```

**Replace with:**
```html
<video autoplay muted loop playsinline>
    <source src="background-video.mp4" type="video/mp4">
    <!-- Fallback for browsers that don't support video -->
</video>
```

**For reseller.html (Reseller Page):**

Find the same section around line 34 and make the same change.

### Step 4: Alternative - Use Different Videos for Each Page

You can use different videos for home and reseller pages:

**Home Page (index.html):**
```html
<source src="home-background.mp4" type="video/mp4">
```

**Reseller Page (reseller.html):**
```html
<source src="reseller-background.mp4" type="video/mp4">
```

### Step 5: Video Optimization Tips

**To optimize your video for web:**

1. **Use online tools** like:
   - HandBrake (free desktop app)
   - CloudConvert.com
   - Online-Convert.com

2. **Compression settings:**
   - Bitrate: 1000-2000 kbps
   - Frame rate: 24-30 fps
   - Remove audio track (not needed)

3. **Create multiple formats** for better browser support:
```html
<video autoplay muted loop playsinline>
    <source src="background-video.webm" type="video/webm">
    <source src="background-video.mp4" type="video/mp4">
</video>
```

### Step 6: Troubleshooting

**If video doesn't play:**
- Check file path is correct
- Ensure video file is in the same folder as HTML files
- Try a different browser
- Check browser console for errors (F12 → Console)

**If video is too bright/distracting:**
- Adjust opacity in CSS (styles.css line ~158):
```css
.hero-video video {
    opacity: 0.2; /* Lower = more subtle */
}
```

**If video doesn't loop:**
- Ensure the `loop` attribute is present in the video tag
- Check that video file isn't corrupted

### Step 7: Using Online Video URLs

**You can also use online video URLs:**
```html
<source src="https://your-video-hosting-site.com/your-video.mp4" type="video/mp4">
```

**Popular video hosting options:**
- Vimeo (get direct MP4 link)
- YouTube (use youtube-dl to get direct link)
- Your own web hosting
- CDN services

### Step 8: Mobile Considerations

**The video is automatically optimized for mobile:**
- Reduced opacity on mobile devices
- Fallback animated background if video fails
- Autoplay may be blocked on some mobile browsers (this is normal)

### Example File Structure

```
your-website-folder/
├── index.html
├── reseller.html
├── styles.css
├── script.js
├── background-video.mp4  ← Your video here
└── VIDEO_SETUP_GUIDE.md
```

### Need Help?

If you need assistance:
1. Check that your video file is in the correct format (MP4)
2. Verify the file path in the HTML matches your video filename exactly
3. Test in different browsers
4. Check browser developer tools for error messages

**Your video should now be playing as the background! 🎬**
